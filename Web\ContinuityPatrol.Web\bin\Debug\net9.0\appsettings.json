{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "None",
      "Microsoft.AspNetCore.Hosting.Diagnostics": "None",
      "Microsoft.EntityFrameworkCore": "None"
    }
  },
  "ApiSettings": {
    "ApiUrl": "https://localhost:2000/"
  },
  "SeqConfig": {
    "ServerUrl": "http://***********:5341",
    "ApiKey": "YNgDU0FtfvHkChDdO2xP",
    "Username": "eP9LNIYSBVwVV4WQ6MducnR//dnewZrRbWBEmjjVHEA=$H3r86NBjEQ3rGpB0mqTxQg8jzo5ghek6mHeq3N+a8vrZ",
    "Password": "drXltNHflna07X6TalHC08EBRH3HKFuTNroCm4LVD7k=$LZKU8c5Upf+0Pt1XGTkmgARhE3Ccf81YC+ClrkH8daXv55AT+Q==",
    "path": "C:\\CP\\Logs\\SeqLogs\\"
  },
  "SignalR": {
    "Url": "https://***********:7079/"
  },
  "ChatBotURL": {
    "Url": "https://***********:8083/"
  },
  "CP": {
    "Version": "6.0"
  },
  "UpdateApiVersion": {
    "Version": "6.0.0"
  },
  "ConnectionStrings": {
    "DBProvider": "905clI+aAqPTFEWo83NkxQ==",
    "Default": "sDA538FU0TFWgDb8pc2LQuEL4OMnK8UZ8v8GGoOBfy5CvGPnfwtlMgKGvfiktn+YXL2mhot166235MbpigQQzH8L5jSCl64929bIO8gT/DdAs3/raHnDP9wdOtY4IOElsIdUycOpOu1VSEDnVRNr8uzmpNQ3UDh0KWzYBwz0hu8cwdgIDVk0TWkYGQ+P6n/lzYqvsE32CUO98ZG2fr7BKxYqYuw4B4YMpsv5fUigXKY="
  },
  "DatabaseSettings": {
    "CommandTimeoutSeconds": 180
  },
  "x-api-key": "pgH7QzFHJx4w46fI~5Uzi4RvtTwlEXp",
  "DataProvider": {
    "Default": "db"
  },
  "Policies": {
    "Default": "localhost"
  },
  "App": {
    "CorsOrigins": "https://localhost:7079/,https://localhost:7079,http://localhost:3001,http://subdomain1.localhost:3000,http://subdomain2.localhost:3000"
  },
  "CacheSettings": {
    "SlidingExpiration": 60
  },
  "JwtSettings": {
    "Key": "84322CFB66934ECC86D547C5CF4F2EFC",
    "Issuer": "http://localhost:8047",
    "Audience": "http://localhost:8047",
    "DurationInMinutes": 60
  },
  "Serilog": {
    "Using": [
      "Serilog.Sinks.File",
      "Serilog.Enrichers.ClientInfo"
    ],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Error",
        "System": "Error",
        "Quartz": "Error"
      }
    },
    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "path": "C:\\CP\\CP_Web_log-.txt",
          "fileSizeLimitBytes": "10485760", // 10 MB
          "rollOnFileSizeLimit": true,      // Enable rolling after 10 MB
          "retainedFileCountLimit": null,   // Keep all rolled files (or set a limit)
          "rollingInterval": "Day",
          "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} | Level: {Level:u3} | EventCode: {EventCode} | CorrID: {CorrID} | User: {User} | IP: {IP} | Server: {Server} | Session: {Session} | Message: {Message:lj}{NewLine}{Exception}"
          // "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{UserName}] [{UserRole}] [{Level:u3}] : [{ClientIp}/{MachineName}/{ThreadId}] - {Message}{NewLine}{Exception}"
        }
      }
      //{
      //  "Name": "Logger",
      //  "Args": {
      //    "configureLogger": {
      //      "Filter": [
      //        {
      //          "Name": "ByIncludingOnly",
      //          "Args": {
      //            "expression": "UserRole = 'SiteAdmin'"
      //          }
      //        }
      //      ],
      //      "WriteTo": [
      //        {
      //          "Name": "File",
      //          "Args": {
      //            "path": "C:\\CP\\Logs\\SiteAdminLogs-.txt",
      //            "fileSizeLimitBytes": "524288000",
      //            "rollOnFileSizeLimit": true,
      //            "retainedFileCountLimit": null,
      //            "rollingInterval": "Day",
      //            "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} | Level: {Level:u3} | EventCode: {EventCode} | CorrID: {CorrID} | User: {User} | IP: {IP} | Server: {Server} | Session: {Session} | Event: {EventName} | Message: {Message:lj}{NewLine}{Exception}"
      //            //"outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{UserName}] [{UserRole}] [{Level:u3}] : [{ClientIp}/{MachineName}/{ThreadId}] - {Message}{NewLine}{Exception}"
      //          }
      //        }
      //      ]
      //    }
      //  }
      //}
    ],
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithThreadId",
      "WithCorrelationId",
      "WithClientIp"
    ]
  },

  "RedisCacheUrl": "127.0.0.1:6379,abortConnect=false,connectTimeout=30000,responseTimeout=30000",
  "AllowedHosts": "*"
}