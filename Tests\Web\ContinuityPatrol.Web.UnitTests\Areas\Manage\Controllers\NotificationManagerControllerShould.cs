﻿using AutoFixture;
using AutoMapper;
using ContinuityPatrol.Application.Features.AlertReceiver.Commands.Create;
using ContinuityPatrol.Application.Features.AlertReceiver.Commands.Update;
using ContinuityPatrol.Application.Features.AlertReceiver.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByBusinessService;
using ContinuityPatrol.Domain.ViewModels.AlertReceiverModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Manage.Controllers;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.UnitTests.Areas.Manage.Controllers;

public class NotificationManagerControllerTests
{
    private readonly Mock<IMapper> _mapperMock = new();
    private readonly Mock<IPublisher> _publisherMock = new();
    private readonly Mock<IDataProvider> _dataProviderMock = new();
    private readonly Mock<ILogger<NotificationManagerController>> _loggerMock = new();
    private NotificationManagerController _controller;

    public NotificationManagerControllerTests()
    {
        Initialize();
    }
    public void Initialize()
    {
        _controller = new NotificationManagerController(
            _mapperMock.Object,
            _publisherMock.Object,
            _dataProviderMock.Object,
            _loggerMock.Object
        );
        _controller.ControllerContext.HttpContext = new DefaultHttpContext();
        _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
    }

    [Fact]
    public async Task List_Returns_View_With_Valid_Model()
    {
        var paginatedResult = new PaginatedResult<AlertReceiverListVm>();

        _dataProviderMock.Setup(p => p.AlertReceiver.GetPaginatedAlertReceivers(It.IsAny<GetAlertReceiverPaginatedListQuery>()))
            .ReturnsAsync(paginatedResult);

        var result = await _controller.List() as ViewResult;

        Assert.NotNull(result);
        Assert.Null(result.Model);
    }

    [Fact]
    public async Task CreateOrUpdate_Create_New_AlertReceiver()
    {
        var viewModel = new Fixture().Create<AlertReceiverViewModal>();
        var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
        dic.Add("id", "22");
        var collection = new FormCollection(dic);
        _controller.Request.Form = collection;
        var command = new CreateAlertReceiverCommand();

        var alertReceiverViewModel = new AlertReceiverViewModal();
        var createCommand = new CreateAlertReceiverCommand();
        var response = new BaseResponse { Success = true, Message = "Created successfully" };

        _mapperMock.Setup(m => m.Map<CreateAlertReceiverCommand>(alertReceiverViewModel))
            .Returns(createCommand);

        _dataProviderMock.Setup(p => p.AlertReceiver.CreateAsync(createCommand))
            .ReturnsAsync(response);

        var result = await _controller.CreateOrUpdate(alertReceiverViewModel) as RedirectToActionResult;

        Assert.Equal("List", result.ActionName);
    }

    [Fact]
    public async Task CreateOrUpdate_Update_Existing_AlertReceiver()
    {
        var viewModel = new Fixture().Create<AlertReceiverViewModal>();
        var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
        dic.Add("id", "22");
        var collection = new FormCollection(dic);
        _controller.Request.Form = collection;
        var command = new UpdateAlertReceiverCommand();

        var alertReceiverViewModel = new AlertReceiverViewModal { Id = "ReceiverId" };
        var updateCommand = new UpdateAlertReceiverCommand();
        var response = new BaseResponse { Success = true, Message = "Updated successfully" };

        _mapperMock.Setup(m => m.Map<UpdateAlertReceiverCommand>(alertReceiverViewModel))
            .Returns(updateCommand);

        _dataProviderMock.Setup(p => p.AlertReceiver.UpdateAsync(updateCommand))
            .ReturnsAsync(response);

        var result = await _controller.CreateOrUpdate(alertReceiverViewModel) as RedirectToActionResult;

        Assert.Equal("List", result.ActionName);
    }

    [Fact]
    public async Task IsAlertReceiverNameExist_Returns_True_If_Name_Exists()
    {
        var alertReceiverName = "TestReceiver";
        var id = "ReceiverId";

        _dataProviderMock.Setup(p => p.AlertReceiver.IsAlertReceiverNameExist(alertReceiverName, id))
            .ReturnsAsync(true);

        var result = await _controller.IsAlertReceiverNameExist(alertReceiverName, id);

        Assert.True(result);
    }

    [Fact]
    public async Task Delete_Removes_AlertReceiver_And_Redirects()
    {
        var id = "ReceiverId";
        var response = new BaseResponse { Success = true, Message = "Deleted successfully" };

        _dataProviderMock.Setup(p => p.AlertReceiver.DeleteAsync(id))
            .ReturnsAsync(response);

        var result = await _controller.Delete(id) as RedirectToActionResult;

        Assert.Equal("List", result.ActionName);
    }

    [Fact]
    public async Task GetPagination_Returns_Json_With_PaginatedList()
    {
        var query = new GetAlertReceiverPaginatedListQuery();
        var paginatedList = new PaginatedResult<AlertReceiverListVm>();

        _dataProviderMock.Setup(p => p.AlertReceiver.GetPaginatedAlertReceivers(query))
            .ReturnsAsync(paginatedList);

        var result = await _controller.GetPagination(query) as JsonResult;

        dynamic jsonResult = result.Value;
        var json = JsonConvert.SerializeObject(result);
        Assert.NotNull(result);
    }

    [Fact]
    public async Task GetAllInfraObjectList_Returns_Json_With_InfraObjectList()
    {
        var infraObjects = new GetUserInfraObjectByBusinessServiceVm();

        _dataProviderMock.Setup(p => p.UserInfraObject.GetUserInfraObjects(It.IsAny<string>()))
            .ReturnsAsync(infraObjects);

        var result = await _controller.GetAllInfraObjectList("test") as JsonResult;

        dynamic jsonResult = result.Value;
        var json = JsonConvert.SerializeObject(result);
        Assert.NotNull(result);
    }
}