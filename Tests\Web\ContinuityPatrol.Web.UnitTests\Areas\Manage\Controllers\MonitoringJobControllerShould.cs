﻿using AutoFixture;
using AutoMapper;
using ContinuityPatrol.Application.Features.GroupPolicy.Queries.GetNames;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetByReplicationType;
using ContinuityPatrol.Application.Features.Job.Commands.Create;
using ContinuityPatrol.Application.Features.Job.Commands.Update;
using ContinuityPatrol.Application.Features.Job.Commands.UpdateJobState;
using ContinuityPatrol.Application.Features.Job.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Template.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.ComponentTypeModel;
using ContinuityPatrol.Domain.ViewModels.GroupPolicyModel;
using ContinuityPatrol.Domain.ViewModels.InfraObjectModel;
using ContinuityPatrol.Domain.ViewModels.JobModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Manage.Controllers;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.UnitTests.Areas.Manage.Controllers
{
    public class MonitoringJobControllerTests
    {
        private readonly Mock<IPublisher> _publisherMock = new();
        private readonly Mock<IJobService> _jobServiceMock = new();
        private readonly Mock<IGroupPolicyService> _groupPolicyServiceMock = new();
        private readonly Mock<IMapper> _mapperMock = new();
        private readonly Mock<ILogger<MonitoringJobController>> _loggerMock = new();
        private readonly Mock<IDataProvider> _dataProviderMock = new();
        private MonitoringJobController _controller;


        public MonitoringJobControllerTests()
        {
            Initialize();
        }
        public void Initialize()
        {
            _controller = new MonitoringJobController(
                _publisherMock.Object,
                //_jobServiceMock.Object,
                //_groupPolicyServiceMock.Object,
                _mapperMock.Object,
                _loggerMock.Object,
                _dataProviderMock.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_Returns_ViewResult_With_JobViewModel()
        {
            var jobPaginatedList = new PaginatedResult<JobListVm>();
            var templateList = new List<GetTemplateListVm>
            {
            new GetTemplateListVm { ReplicationTypeId = "1", ReplicationTypeName = "Monitoring" }
            };
            var infraObjectNames = new List<GetInfraObjectNameVm>();
            var groupPolicies = new List<GroupPolicyListVm>();
            var componentTypes = new List<ComponentTypeModel>
            {
            new ComponentTypeModel { Id = "1", Properties = "{\"name\": \"Replication\"}" }
            };

			_dataProviderMock.Setup(js => js.JobService.GetJobPaginatedList(It.IsAny<GetJobPaginatedListQuery>()))
                .ReturnsAsync(jobPaginatedList);
            _dataProviderMock.Setup(dp => dp.Template.GetTemplateList())
                .ReturnsAsync(templateList);
            _dataProviderMock.Setup(dp => dp.InfraObject.GetInfraObjectNames())
                .ReturnsAsync(infraObjectNames);
            _dataProviderMock.Setup(dp => dp.GroupPolicy.GetGroupPolicies())
                .ReturnsAsync(groupPolicies);
            _dataProviderMock.Setup(dp => dp.ComponentType.GetComponentTypeListByName("Replication"))
                .ReturnsAsync(componentTypes);

            var result = await _controller.List() as ViewResult;

            var model = Assert.IsType<JobViewModel>(result.Model);
            Assert.NotNull(model);
            Assert.Equal(infraObjectNames, model.InfraObjectNameVms);
            Assert.Equal(groupPolicies, model.GroupPolicies);
        }

        [Fact]
        public async Task CreateOrUpdate_Creates_Job_If_No_Id()
        {
            var viewModel = new Fixture().Create<JobViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new CreateJobCommand();

            var jobModel = new JobViewModel { Id = null };
            var createCommand = new CreateJobCommand();
            var response = new BaseResponse { Success = true };

            _mapperMock.Setup(m => m.Map<CreateJobCommand>(jobModel)).Returns(createCommand);
			_dataProviderMock.Setup(js => js.JobService.CreateAsync(createCommand)).ReturnsAsync(response);

            var result = await _controller.CreateOrUpdate(jobModel);

			_dataProviderMock.Verify(js => js.JobService.CreateAsync(It.IsAny<CreateJobCommand>()), Times.Once);
        }

        [Fact]
        public async Task CreateOrUpdate_Updates_Job_If_Id_Exists()
        {
            var viewModel = new Fixture().Create<JobViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new UpdateJobCommand();

            var jobModel = new JobViewModel { Id = "some-id" };
            var updateCommand = new UpdateJobCommand();
            var response = new BaseResponse { Success = true };

            _mapperMock.Setup(m => m.Map<UpdateJobCommand>(jobModel)).Returns(updateCommand);
			_dataProviderMock.Setup(js => js.JobService.UpdateAsync(updateCommand)).ReturnsAsync(response);

            var result = await _controller.CreateOrUpdate(jobModel);

			_dataProviderMock.Verify(js => js.JobService.UpdateAsync(It.IsAny<UpdateJobCommand>()), Times.Once);
        }

        [Fact]
        public async Task Resetjob_Resets_Job_To_Pending()
        {
            var jobModel = new JobViewModel { Id = "job-id", Status = "Completed" };
            var updateCommand = new UpdateJobCommand { Id = "job-id", Status = "Pending" };
            var response = new BaseResponse { Success = true };

            _mapperMock.Setup(m => m.Map<UpdateJobCommand>(jobModel)).Returns(updateCommand);
			_dataProviderMock.Setup(js => js.JobService.UpdateAsync(updateCommand)).ReturnsAsync(response);

            var result = await _controller.ResetMonitoringJob(jobModel) as JsonResult;

			_dataProviderMock.Verify(js => js.JobService.UpdateAsync(It.IsAny<UpdateJobCommand>()), Times.Once);
            var json = JsonConvert.SerializeObject(result);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task List_Returns_ViewResult_On_Exception()
        {
			_dataProviderMock.Setup(js => js.JobService.GetJobPaginatedList(It.IsAny<GetJobPaginatedListQuery>()))
                .ThrowsAsync(new Exception("Test Exception"));

            var result = await _controller.List();

            var viewResult = Assert.IsType<ViewResult>(result);
            Assert.Equal("List", viewResult.ViewName);
        }

        [Fact]
        public async Task Delete_Returns_RouteToPostView_When_Success()
        {
            var id = "job-id";
            var response = new BaseResponse { Success = true };

			_dataProviderMock.Setup(js => js.JobService.DeleteAsync(id))
                .ReturnsAsync(response);

            var result = await _controller.Delete(id) as RedirectToActionResult;

            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task Delete_Returns_ErrorMessage_On_Exception()
        {
            var id = "job-id";

            _jobServiceMock.Setup(js => js.DeleteAsync(id))
                .ThrowsAsync(new System.Exception("Delete error"));

            var result = await _controller.Delete(id) as RedirectToActionResult;

            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task IsJobNameExist_Returns_True_If_Job_Exists()
        {
            var name = "jobName";
            var id = "job-id";

			_dataProviderMock.Setup(js => js.JobService.IsJobNameExist(name, id))
                .ReturnsAsync(true);

            var result = await _controller.IsJobNameExist(name, id);

            Assert.True(result);
        }

        [Fact]
        public async Task IsJobNameExist_Returns_False_On_Exception()
        {
            var name = "jobName";
            var id = "job-id";

            _jobServiceMock.Setup(js => js.IsJobNameExist(name, id))
                .ThrowsAsync(new System.Exception("Exception in IsJobNameExist"));

            var result = await _controller.IsJobNameExist(name, id);

            Assert.False(result);
        }

        [Fact]
        public async Task GetPagination_Returns_Json_With_Paginated_List()
        {
            var query = new GetJobPaginatedListQuery();
            var paginatedList = new PaginatedResult<JobListVm>();

            _jobServiceMock.Setup(js => js.GetJobPaginatedList(query))
                .ReturnsAsync(paginatedList);

            var result = await _controller.GetPagination(query) as JsonResult;

            dynamic jsonResponse = result.Value;
           
        }

        [Fact]
        public async Task GetGroupPolicies_Returns_Json_With_GroupPolicies()
        {
            var groupPolicies = new List<GroupPolicyNameVm>();

			_dataProviderMock.Setup(gps => gps.GroupPolicy.GetGroupPolicyNames())
                .ReturnsAsync(groupPolicies);

            var result = await _controller.GetGroupPolicyNames() as JsonResult;

            Assert.Equal(groupPolicies, result.Value);
        }

        [Fact]
        public async Task GetJobState_Returns_Json_With_UpdatedState()
        {
            var updateCommand = new UpdateJobStateCommand();
            var updatedState = new BaseResponse { Success = true };

            _jobServiceMock.Setup(js => js.UpdateJobState(updateCommand))
                .ReturnsAsync(updatedState);

            var result = await _controller.UpdateJobState(updateCommand) as JsonResult;

            dynamic jsonResponse = result.Value;
            var json = JsonConvert.SerializeObject(result);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetInfraObjectDetails_Returns_Json_With_Data_If_Valid_ReplicationTypeId()
        {
            var replicationTypeId = "replication-type-id";
            var infraObjects = new List<InfraObjectListByReplicationTypeVm>();

            _dataProviderMock.Setup(dp => dp.InfraObject.GetInfraObjectListByReplicationTypeId(replicationTypeId))
                .ReturnsAsync(infraObjects);

            var result = await _controller.GetInfraObjectListByReplicationTypeId(replicationTypeId) as JsonResult;

            dynamic jsonResponse = result.Value;
            var json = JsonConvert.SerializeObject(result);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetInfraObjectDetails_Returns_Error_If_Invalid_ReplicationTypeId()
        {
            var result = await _controller.GetInfraObjectListByReplicationTypeId(null) as JsonResult;

            dynamic jsonResponse = result.Value;
            var json = JsonConvert.SerializeObject(result);
            Assert.NotNull(result);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetInfraObjectDetails_Returns_Error_On_Exception()
        {
            var replicationTypeId = "replication-type-id";

            _dataProviderMock.Setup(dp => dp.InfraObject.GetInfraObjectListByReplicationTypeId(replicationTypeId))
                .ThrowsAsync(new System.Exception("Exception in InfraObjectDetails"));

            var result = await _controller.GetInfraObjectListByReplicationTypeId(replicationTypeId) as JsonResult;

            dynamic jsonResponse = result.Value;
            Assert.False(jsonResponse.Success);
        }
    }
}
