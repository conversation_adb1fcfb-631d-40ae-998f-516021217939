﻿using AutoMapper;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.Create;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.Update;
using ContinuityPatrol.Application.Features.ReplicationJob.Commands.UpdateReplicationJobState;
using ContinuityPatrol.Application.Features.ReplicationJob.Queries.GetPaginationList;
using ContinuityPatrol.Application.Features.Template.Queries.GetList;
using ContinuityPatrol.Domain.ViewModels.ComponentTypeModel;
using ContinuityPatrol.Domain.ViewModels.GroupPolicyModel;
using ContinuityPatrol.Domain.ViewModels.InfraObjectModel;
using ContinuityPatrol.Domain.ViewModels.ReplicationJobModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Manage.Controllers;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.UnitTests.Areas.Manage.Controllers
{
    public class ReplicationJobControllerTests
    {
        private readonly Mock<IPublisher> _publisherMock = new();
        private readonly Mock<IReplicationJobService> _replicationJobServiceMock = new();
        private readonly Mock<IGroupPolicyService> _groupPolicyServiceMock = new();
        private readonly Mock<IMapper> _mapperMock = new();
        private readonly Mock<ILogger<JobManagementController>> _loggerMock = new();
        private readonly Mock<IDataProvider> _dataProviderMock = new();
        private ReplicationJobController _controller;

        public ReplicationJobControllerTests()
        {
            Initialize();
        }
        public void Initialize()
        {
            _controller = new ReplicationJobController(
                _publisherMock.Object,
               // _replicationJobServiceMock.Object,
               // _groupPolicyServiceMock.Object,
                _mapperMock.Object,
                _loggerMock.Object,
                _dataProviderMock.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_Returns_View_With_Valid_Model()
        {
           
            //var paginatedList = new PaginatedResult<ReplicationJobListVm>();
            var templates = new List<GetTemplateListVm> { new GetTemplateListVm { ReplicationTypeId = "1", ReplicationTypeName = "Template1" } };
            var infraObjects = new List<GetInfraObjectNameVm> { };
            var groupPolicies = new List<GroupPolicyListVm> { };
            var componentTypes = new List<ComponentTypeModel>
            {
            new ComponentTypeModel { Id = "1", Properties = "{\"name\":\"ReplicationType1\"}" }
            };

			/*_dataProviderMock.Setup(p => p.ReplicationJobService.GetPaginated(It.IsAny<GetReplicationJobPaginatedListQuery>()))
                .ReturnsAsync(paginatedList);*/

            _dataProviderMock.Setup(p => p.Template.GetTemplateList())
                .ReturnsAsync(templates);
            _dataProviderMock.Setup(p => p.InfraObject.GetInfraObjectNames())
                .ReturnsAsync(infraObjects);
            _dataProviderMock.Setup(p => p.GroupPolicy.GetGroupPolicies())
                .ReturnsAsync(groupPolicies);
            _dataProviderMock.Setup(p => p.ComponentType.GetComponentTypeListByName("Replication"))
                .ReturnsAsync(componentTypes);

            var result = await _controller.List() as ViewResult;

            var model = result.Model as ReplicationJobViewModel;
            Assert.NotNull(model);
            //Assert.Equal(paginatedList, model.PaginatedJob);
            Assert.Equal(infraObjects, model.InfraObjectNameVms);
            Assert.Equal(groupPolicies, model.GroupPolicies);
            Assert.Equal("1", model.ReplicationTypes["ReplicationType1"]);
        }

        [Fact]
        public async Task CreateOrUpdate_Create_New_ReplicationJob()
        {
            var command = new CreateReplicationJobCommand();
            var replicationJobModel = new ReplicationJobListVm();
            var createCommand = new CreateReplicationJobCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mapperMock.Setup(m => m.Map<CreateReplicationJobCommand>(replicationJobModel))
                .Returns(createCommand);

            _replicationJobServiceMock.Setup(p => p.CreateReplicationJob(createCommand))
                .ReturnsAsync(response);

            var result = await _controller.CreateOrUpdate(replicationJobModel) as RedirectToActionResult;

            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task CreateOrUpdate_Update_Existing_ReplicationJob()
        {
            var replicationJobModel = new ReplicationJobListVm { Id = "1" };
            var updateCommand = new UpdateReplicationJobCommand();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mapperMock.Setup(m => m.Map<UpdateReplicationJobCommand>(replicationJobModel))
                .Returns(updateCommand);

            _replicationJobServiceMock.Setup(p => p.UpdateReplicationJob(updateCommand))
                .ReturnsAsync(response);

            var result = await _controller.CreateOrUpdate(replicationJobModel) as RedirectToActionResult;

            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task Delete_Removes_ReplicationJob_And_Redirects()
        {
            var id = "1";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };

			_dataProviderMock.Setup(p => p.ReplicationJobService.DeleteReplicationJob(id))
                .ReturnsAsync(response);

            var result = await _controller.Delete(id) as RedirectToActionResult;

            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task IsReplicationJobNameExist_Returns_True_If_Name_Exists()
        {
            var jobName = "ReplicationJob1";
            var id = "1";

			_dataProviderMock.Setup(p => p.ReplicationJobService.IsReplicationJobNameExist(jobName, id))
                .ReturnsAsync(true);

            var result = await _controller.IsReplicationJobNameExist(jobName, id);

            Assert.True(result);
        }

        [Fact]
        public async Task getjobstate_Updates_ReplicationJobState_Returns_Json()
        {
            var stateCommand = new UpdateReplicationJobStateCommand { };
            var stateResponse = new BaseResponse { Success = true, Message = "State updated" };

            _replicationJobServiceMock.Setup(p => p.UpdateReplicationJobState(stateCommand))
                .ReturnsAsync(stateResponse);

            var result = await _controller.UpdateReplicationJobState(stateCommand) as JsonResult;

            dynamic jsonResult = result.Value;
            var json = JsonConvert.SerializeObject(result);
            Assert.NotNull(result);
        }

        [Fact]
        public async Task Resetjob_Resets_Job_Status_To_Pending()
        {
            var jobModel = new ReplicationJobListVm { Id = "1", Status = "Completed" };
            var updateCommand = new UpdateReplicationJobCommand();
            var response = new BaseResponse { Success = true, Message = "Job reset to pending" };

            _mapperMock.Setup(m => m.Map<UpdateReplicationJobCommand>(jobModel))
                .Returns(updateCommand);

			_dataProviderMock.Setup(p => p.ReplicationJobService.UpdateReplicationJob(updateCommand))
                .ReturnsAsync(response);

            var result = await _controller.ResetJobStatus(jobModel) as JsonResult;

            var json = JsonConvert.SerializeObject(result.Value);
            Assert.Contains("\"Success\":true", json);
        }

        [Fact]
        public async Task GetPagination_Returns_Json_With_PaginatedList()
        {
            var query = new GetReplicationJobPaginatedListQuery();
            var paginatedList = new PaginatedResult<ReplicationJobListVm>();

            _replicationJobServiceMock.Setup(p => p.GetPaginated(query))
                .ReturnsAsync(paginatedList);

            var result = await _controller.GetPagination(query) as JsonResult;

            dynamic jsonResult = result.Value;
            var json = JsonConvert.SerializeObject(result);
            Assert.NotNull(result);
        }
    }
}
