{"Version": 1, "WorkspaceRootPath": "D:\\Testcase\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|d:\\testcase\\tests\\web\\continuitypatrol.web.unittests\\areas\\itautomation\\controllers\\workflowinfraobjectcontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|solutionrelative:tests\\web\\continuitypatrol.web.unittests\\areas\\itautomation\\controllers\\workflowinfraobjectcontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|d:\\testcase\\web\\continuitypatrol.web\\areas\\itautomation\\controllers\\workflowinfraobjectcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|solutionrelative:web\\continuitypatrol.web\\areas\\itautomation\\controllers\\workflowinfraobjectcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4CF459FD-E8DA-4E30-B583-C5AAA246CDB5}|Shared\\ContinuityPatrol.Shared.Services\\ContinuityPatrol.Shared.Services.csproj|d:\\testcase\\shared\\continuitypatrol.shared.services\\contract\\iworkflowinfraobjectservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4CF459FD-E8DA-4E30-B583-C5AAA246CDB5}|Shared\\ContinuityPatrol.Shared.Services\\ContinuityPatrol.Shared.Services.csproj|solutionrelative:shared\\continuitypatrol.shared.services\\contract\\iworkflowinfraobjectservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|d:\\testcase\\tests\\web\\continuitypatrol.web.unittests\\areas\\manage\\controllers\\monitoringservicescontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|solutionrelative:tests\\web\\continuitypatrol.web.unittests\\areas\\manage\\controllers\\monitoringservicescontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|d:\\testcase\\web\\continuitypatrol.web\\areas\\manage\\controllers\\monitoringservicescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|solutionrelative:web\\continuitypatrol.web\\areas\\manage\\controllers\\monitoringservicescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|d:\\testcase\\tests\\web\\continuitypatrol.web.unittests\\areas\\manage\\controllers\\monitoringjobcontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|solutionrelative:tests\\web\\continuitypatrol.web.unittests\\areas\\manage\\controllers\\monitoringjobcontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|d:\\testcase\\web\\continuitypatrol.web\\areas\\manage\\controllers\\monitoringjobcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|solutionrelative:web\\continuitypatrol.web\\areas\\manage\\controllers\\monitoringjobcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{81043510-5A60-47AC-B36C-142D8BA72226}|Core\\ContinuityPatrol.Domain\\ContinuityPatrol.Domain.csproj|d:\\testcase\\core\\continuitypatrol.domain\\viewmodels\\jobmodel\\jobviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{81043510-5A60-47AC-B36C-142D8BA72226}|Core\\ContinuityPatrol.Domain\\ContinuityPatrol.Domain.csproj|solutionrelative:core\\continuitypatrol.domain\\viewmodels\\jobmodel\\jobviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|d:\\testcase\\tests\\web\\continuitypatrol.web.unittests\\areas\\manage\\controllers\\escalationmatrixcontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B473E785-F4D0-4E26-AA6A-B9BB2CF722DA}|Tests\\Web\\ContinuityPatrol.Web.UnitTests\\ContinuityPatrol.Web.UnitTests.csproj|solutionrelative:tests\\web\\continuitypatrol.web.unittests\\areas\\manage\\controllers\\escalationmatrixcontrollershould.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|d:\\testcase\\web\\continuitypatrol.web\\areas\\manage\\controllers\\escalationmatrixcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E09C9876-64F6-4960-A594-04FFB7522410}|Web\\ContinuityPatrol.Web\\ContinuityPatrol.Web.csproj|solutionrelative:web\\continuitypatrol.web\\areas\\manage\\controllers\\escalationmatrixcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4CF459FD-E8DA-4E30-B583-C5AAA246CDB5}|Shared\\ContinuityPatrol.Shared.Services\\ContinuityPatrol.Shared.Services.csproj|d:\\testcase\\shared\\continuitypatrol.shared.services\\contract\\iuserservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4CF459FD-E8DA-4E30-B583-C5AAA246CDB5}|Shared\\ContinuityPatrol.Shared.Services\\ContinuityPatrol.Shared.Services.csproj|solutionrelative:shared\\continuitypatrol.shared.services\\contract\\iuserservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{81043510-5A60-47AC-B36C-142D8BA72226}|Core\\ContinuityPatrol.Domain\\ContinuityPatrol.Domain.csproj|d:\\testcase\\core\\continuitypatrol.domain\\viewmodels\\escalationmatrix\\escalationmatrixviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{81043510-5A60-47AC-B36C-142D8BA72226}|Core\\ContinuityPatrol.Domain\\ContinuityPatrol.Domain.csproj|solutionrelative:core\\continuitypatrol.domain\\viewmodels\\escalationmatrix\\escalationmatrixviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4CF459FD-E8DA-4E30-B583-C5AAA246CDB5}|Shared\\ContinuityPatrol.Shared.Services\\ContinuityPatrol.Shared.Services.csproj|d:\\testcase\\shared\\continuitypatrol.shared.services\\contract\\iteammasterservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4CF459FD-E8DA-4E30-B583-C5AAA246CDB5}|Shared\\ContinuityPatrol.Shared.Services\\ContinuityPatrol.Shared.Services.csproj|solutionrelative:shared\\continuitypatrol.shared.services\\contract\\iteammasterservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2950A847-AFC7-4861-9515-F23C46CCE376}|Shared\\ContinuityPatrol.Shared.Core\\ContinuityPatrol.Shared.Core.csproj|d:\\testcase\\shared\\continuitypatrol.shared.core\\constants\\eventcodes.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2950A847-AFC7-4861-9515-F23C46CCE376}|Shared\\ContinuityPatrol.Shared.Core\\ContinuityPatrol.Shared.Core.csproj|solutionrelative:shared\\continuitypatrol.shared.core\\constants\\eventcodes.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4CF459FD-E8DA-4E30-B583-C5AAA246CDB5}|Shared\\ContinuityPatrol.Shared.Services\\ContinuityPatrol.Shared.Services.csproj|d:\\testcase\\shared\\continuitypatrol.shared.services\\contract\\iescalationmatrixservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4CF459FD-E8DA-4E30-B583-C5AAA246CDB5}|Shared\\ContinuityPatrol.Shared.Services\\ContinuityPatrol.Shared.Services.csproj|solutionrelative:shared\\continuitypatrol.shared.services\\contract\\iescalationmatrixservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "WorkflowInfraObjectControllerShould.cs", "DocumentMoniker": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\ITAutomation\\Controllers\\WorkflowInfraObjectControllerShould.cs", "RelativeDocumentMoniker": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\ITAutomation\\Controllers\\WorkflowInfraObjectControllerShould.cs", "ToolTip": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\ITAutomation\\Controllers\\WorkflowInfraObjectControllerShould.cs*", "RelativeToolTip": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\ITAutomation\\Controllers\\WorkflowInfraObjectControllerShould.cs*", "ViewState": "AgIAAA8AAAAAAAAAAIAwwCgAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T12:50:20.822Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "IWorkflowInfraObjectService.cs", "DocumentMoniker": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Services\\Contract\\IWorkflowInfraObjectService.cs", "RelativeDocumentMoniker": "Shared\\ContinuityPatrol.Shared.Services\\Contract\\IWorkflowInfraObjectService.cs", "ToolTip": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Services\\Contract\\IWorkflowInfraObjectService.cs", "RelativeToolTip": "Shared\\ContinuityPatrol.Shared.Services\\Contract\\IWorkflowInfraObjectService.cs", "ViewState": "AgIAAAUAAAAAAAAAAAAowBEAAABCAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T12:48:08.689Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "WorkflowInfraObjectController.cs", "DocumentMoniker": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\ITAutomation\\Controllers\\WorkflowInfraObjectController.cs", "RelativeDocumentMoniker": "Web\\ContinuityPatrol.Web\\Areas\\ITAutomation\\Controllers\\WorkflowInfraObjectController.cs", "ToolTip": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\ITAutomation\\Controllers\\WorkflowInfraObjectController.cs", "RelativeToolTip": "Web\\ContinuityPatrol.Web\\Areas\\ITAutomation\\Controllers\\WorkflowInfraObjectController.cs", "ViewState": "AgIAABUAAAAAAAAAAAAowC4AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T12:45:15.956Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "MonitoringServicesControllerShould.cs", "DocumentMoniker": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Manage\\Controllers\\MonitoringServicesControllerShould.cs", "RelativeDocumentMoniker": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Manage\\Controllers\\MonitoringServicesControllerShould.cs", "ToolTip": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Manage\\Controllers\\MonitoringServicesControllerShould.cs", "RelativeToolTip": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Manage\\Controllers\\MonitoringServicesControllerShould.cs", "ViewState": "AgIAAFQAAAAAAAAAAAAQwJwAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T12:28:49.268Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "MonitoringServicesController.cs", "DocumentMoniker": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Manage\\Controllers\\MonitoringServicesController.cs", "RelativeDocumentMoniker": "Web\\ContinuityPatrol.Web\\Areas\\Manage\\Controllers\\MonitoringServicesController.cs", "ToolTip": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Manage\\Controllers\\MonitoringServicesController.cs", "RelativeToolTip": "Web\\ContinuityPatrol.Web\\Areas\\Manage\\Controllers\\MonitoringServicesController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAYwN0AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T12:29:10.898Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "JobViewModel.cs", "DocumentMoniker": "D:\\Testcase\\Core\\ContinuityPatrol.Domain\\ViewModels\\JobModel\\JobViewModel.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Domain\\ViewModels\\JobModel\\JobViewModel.cs", "ToolTip": "D:\\Testcase\\Core\\ContinuityPatrol.Domain\\ViewModels\\JobModel\\JobViewModel.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Domain\\ViewModels\\JobModel\\JobViewModel.cs", "ViewState": "AgIAABkAAAAAAAAAAAA1wCYAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T12:17:27.583Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "MonitoringJobController.cs", "DocumentMoniker": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Manage\\Controllers\\MonitoringJobController.cs", "RelativeDocumentMoniker": "Web\\ContinuityPatrol.Web\\Areas\\Manage\\Controllers\\MonitoringJobController.cs", "ToolTip": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Manage\\Controllers\\MonitoringJobController.cs", "RelativeToolTip": "Web\\ContinuityPatrol.Web\\Areas\\Manage\\Controllers\\MonitoringJobController.cs", "ViewState": "AgIAALcAAAAAAAAAAAAYwMIAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T12:15:19.754Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "MonitoringJobControllerShould.cs", "DocumentMoniker": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Manage\\Controllers\\MonitoringJobControllerShould.cs", "RelativeDocumentMoniker": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Manage\\Controllers\\MonitoringJobControllerShould.cs", "ToolTip": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Manage\\Controllers\\MonitoringJobControllerShould.cs", "RelativeToolTip": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Manage\\Controllers\\MonitoringJobControllerShould.cs", "ViewState": "AgIAAKEAAAAAAAAAAAApwLAAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T12:14:38.332Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "IUserService.cs", "DocumentMoniker": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Services\\Contract\\IUserService.cs", "RelativeDocumentMoniker": "Shared\\ContinuityPatrol.Shared.Services\\Contract\\IUserService.cs", "ToolTip": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Services\\Contract\\IUserService.cs", "RelativeToolTip": "Shared\\ContinuityPatrol.Shared.Services\\Contract\\IUserService.cs", "ViewState": "AgIAABIAAAAAAAAAAAAwwB8AAAAqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T12:10:04.237Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "EscalationMatrixViewModel.cs", "DocumentMoniker": "D:\\Testcase\\Core\\ContinuityPatrol.Domain\\ViewModels\\EscalationMatrix\\EscalationMatrixViewModel.cs", "RelativeDocumentMoniker": "Core\\ContinuityPatrol.Domain\\ViewModels\\EscalationMatrix\\EscalationMatrixViewModel.cs", "ToolTip": "D:\\Testcase\\Core\\ContinuityPatrol.Domain\\ViewModels\\EscalationMatrix\\EscalationMatrixViewModel.cs", "RelativeToolTip": "Core\\ContinuityPatrol.Domain\\ViewModels\\EscalationMatrix\\EscalationMatrixViewModel.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAQwAcAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T12:06:51.132Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "ITeamMasterService.cs", "DocumentMoniker": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Services\\Contract\\ITeamMasterService.cs", "RelativeDocumentMoniker": "Shared\\ContinuityPatrol.Shared.Services\\Contract\\ITeamMasterService.cs", "ToolTip": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Services\\Contract\\ITeamMasterService.cs", "RelativeToolTip": "Shared\\ContinuityPatrol.Shared.Services\\Contract\\ITeamMasterService.cs", "ViewState": "AgIAAAoAAAAAAAAAAIAwwBwAAAAwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T12:05:39.019Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "EventCodes.cs", "DocumentMoniker": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Core\\Constants\\EventCodes.cs", "RelativeDocumentMoniker": "Shared\\ContinuityPatrol.Shared.Core\\Constants\\EventCodes.cs", "ToolTip": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Core\\Constants\\EventCodes.cs", "RelativeToolTip": "Shared\\ContinuityPatrol.Shared.Core\\Constants\\EventCodes.cs", "ViewState": "AgIAAK0EAAAAAAAAAAAlwIgEAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T11:47:40.797Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "EscalationMatrixController.cs", "DocumentMoniker": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Manage\\Controllers\\EscalationMatrixController.cs", "RelativeDocumentMoniker": "Web\\ContinuityPatrol.Web\\Areas\\Manage\\Controllers\\EscalationMatrixController.cs", "ToolTip": "D:\\Testcase\\Web\\ContinuityPatrol.Web\\Areas\\Manage\\Controllers\\EscalationMatrixController.cs", "RelativeToolTip": "Web\\ContinuityPatrol.Web\\Areas\\Manage\\Controllers\\EscalationMatrixController.cs", "ViewState": "AgIAABUAAAAAAAAAAAAYwDMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T11:43:20.286Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "IEscalationMatrixService.cs", "DocumentMoniker": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Services\\Contract\\IEscalationMatrixService.cs", "RelativeDocumentMoniker": "Shared\\ContinuityPatrol.Shared.Services\\Contract\\IEscalationMatrixService.cs", "ToolTip": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Services\\Contract\\IEscalationMatrixService.cs", "RelativeToolTip": "Shared\\ContinuityPatrol.Shared.Services\\Contract\\IEscalationMatrixService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T11:43:06.182Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "EscalationMatrixControllerShould.cs", "DocumentMoniker": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Manage\\Controllers\\EscalationMatrixControllerShould.cs", "RelativeDocumentMoniker": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Manage\\Controllers\\EscalationMatrixControllerShould.cs", "ToolTip": "D:\\Testcase\\Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Manage\\Controllers\\EscalationMatrixControllerShould.cs", "RelativeToolTip": "Tests\\Web\\ContinuityPatrol.Web.UnitTests\\Areas\\Manage\\Controllers\\EscalationMatrixControllerShould.cs", "ViewState": "AgIAADEAAAAAAAAAAIAwwEEAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T11:42:23.225Z", "EditorCaption": ""}]}, {"DockedWidth": 200, "SelectedChildIndex": -1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{5726b0e3-1012-5233-81f9-d1fad48e7a56}"}, {"$type": "Bookmark", "Name": "ST:0:0:{e1b7d1f8-9b3c-49b1-8f4f-bfc63a88835d}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{554c35d9-bf5e-5ffd-80de-932222077110}"}]}]}]}