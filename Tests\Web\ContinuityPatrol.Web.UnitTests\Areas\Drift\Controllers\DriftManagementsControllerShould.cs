﻿using AutoFixture;
using AutoMapper;
using ContinuityPatrol.Application.Features.DriftJob.Commands.Create;
using ContinuityPatrol.Application.Features.DriftJob.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.Entities;
using ContinuityPatrol.Domain.ViewModels.ComponentTypeModel;
using ContinuityPatrol.Domain.ViewModels.DriftJobModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Drift.Controllers;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.UnitTests.Areas.Drift.Controllers
{
	public class DriftManagementsControllerTests
	{
		private readonly Mock<IDataProvider> _mockDataProvider;
		private readonly Mock<IMapper> _mockMapper;
		private readonly Mock<ILogger<DriftManagementController>> _mockLogger;
        private readonly Mock<IPublisher> _mockPublisher = new();
        private readonly DriftManagementController _controller;

		public DriftManagementsControllerTests()
		{
			_mockDataProvider = new Mock<IDataProvider>();
			_mockMapper = new Mock<IMapper>();
			_mockLogger = new Mock<ILogger<DriftManagementController>>();
			_controller = new DriftManagementController(_mockLogger.Object, _mockDataProvider.Object, _mockMapper.Object, _mockPublisher.Object);
			_controller.ControllerContext.HttpContext = new DefaultHttpContext();
			_controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
		}

		[Fact]
		public async Task List_ShouldReturnView()
		{
			
			var result = await _controller.List();

			
			var viewResult = Assert.IsType<ViewResult>(result);
			Assert.NotNull(viewResult);
		}

		[Fact]
		public async Task GetSolutionDetails_ShouldReturnJson_WithSuccess()
		{
			
			var replicationTypeList = new List<string> { "Type1", "Type2" };
			var componenet = new List<ComponentTypeModel>();
			_mockDataProvider.Setup(x => x.ComponentType.GetComponentTypeListByName("Replication"))
							 .ReturnsAsync(componenet);

			
			var result = await _controller.GetSolutionDetails() as ActionResult;

			
			var jsonResult = Assert.IsType<JsonResult>(result);
			var json = JsonConvert.SerializeObject(jsonResult.Value);
			Assert.Contains("\"Success\":true", json);
			Assert.NotNull(result);
			
		}

		[Fact]
		public async Task GetSolutionDetails_ShouldReturnJson_WithError()
		{
			
			_mockDataProvider.Setup(x => x.ComponentType.GetComponentTypeListByName("Replication"))
							 .ThrowsAsync(new Exception("Error retrieving data"));

			
			var result = await _controller.GetSolutionDetails();

			
			var jsonResult = Assert.IsType<JsonResult>(result);
			var json = JsonConvert.SerializeObject(jsonResult.Value);
			Assert.Contains("\"Success\":false", json);
			Assert.NotNull(result);
		}

		[Fact]
		public async Task GetPagination_ShouldReturnJson_WithSuccess()
		{
			
			var query = new GetDriftJobPaginatedListQuery();
			var driftJobs = new List<DriftJob>(); // Assume DriftJob is a valid model.
			var pagination = new PaginatedResult<DriftJobListVm>();
			_mockDataProvider.Setup(x => x.DriftJob.GetPaginatedDriftJobs(query))
							 .ReturnsAsync(pagination);

			
			var result = await _controller.GetPagination(query);

			
			var jsonResult = Assert.IsType<JsonResult>(result);
			var json = JsonConvert.SerializeObject(jsonResult.Value);
			Assert.Contains("\"Success\":true", json);
			Assert.NotNull(result);
			
		}

		[Fact]
		public async Task CreateOrUpdate_ShouldReturnJson_OnCreate()
		{
			
			var parameterListVm = new AutoFixture.Fixture().Create<DriftJobListVm>();
			var command = new CreateDriftJobCommand();
			var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
			dic.Add("id", "");
			var collection = new FormCollection(dic);
			_controller.Request.Form = collection;
			var response = new BaseResponse { Success = true };

			_mockMapper.Setup(m => m.Map<CreateDriftJobCommand>(parameterListVm)).Returns(command);
			_mockDataProvider.Setup(x => x.DriftJob.CreateAsync(command)).ReturnsAsync(response);

			
			var result = await _controller.CreateOrUpdate(parameterListVm);

			
			var jsonResult = Assert.IsType<JsonResult>(result);
			var json = JsonConvert.SerializeObject(jsonResult.Value);
			Assert.Contains("\"Success\":true", json);
			Assert.NotNull(result);
		}

		[Fact]
		public async Task Delete_ShouldReturnJson_WithSuccess()
		{
			
			var id = "test-id";
			_mockDataProvider.Setup(x => x.DriftJob.DeleteAsync(id)).ReturnsAsync(new BaseResponse {Success=true,Message ="Deleted successfuly"});

			
			var result = await _controller.Delete(id);

			
			var jsonResult = Assert.IsType<JsonResult>(result);
			var json = JsonConvert.SerializeObject(jsonResult.Value);
			Assert.Contains("\"Success\":true", json);
			Assert.NotNull(result);
		}

		[Fact]
		public async Task Delete_ShouldReturnJson_WithError()
		{
			
			var id = "test-id";
			_mockDataProvider.Setup(x => x.DriftJob.DeleteAsync(id)).ThrowsAsync(new Exception("Deletion error"));

			
			var result = await _controller.Delete(id);

			
			var jsonResult = Assert.IsType<JsonResult>(result);
			var json = JsonConvert.SerializeObject(jsonResult.Value);
			Assert.Contains("\"Success\":false", json);
			Assert.NotNull(result);
		}
	}
}
