﻿using ContinuityPatrol.Application.Features.WorkflowInfraObject.Queries.GetByInfraObjectIdAndActionType;
using ContinuityPatrol.Domain.ViewModels.WorkflowInfraObjectModel;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Areas.ITAutomation.Controllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json.Linq;

namespace ContinuityPatrol.Web.UnitTests.Areas.ITAutomation.Controllers;

public class WorkflowInfraObjectControllerTests
{
    private readonly Mock<IDataProvider> _providerMock;
    private readonly WorkflowInfraObjectController _controller;
	private Mock<ILogger<WorkflowInfraObjectController>> logger = new();
	public WorkflowInfraObjectControllerTests()
    {
        _providerMock = new Mock<IDataProvider>();
        _controller = new WorkflowInfraObjectController(_providerMock.Object, logger.Object);
    }

    [Fact]
    public async Task GetWorkflowByInfraObjectIdAndActionType_ReturnsJsonResult_WithWorkflow()
    {
        var infraId = "infra123";
        var actionType = "actionType1";
        var workflow = new List<WorkflowInfraObjectByInfraObjectIdAndActionTypeVm>
        {
            new WorkflowInfraObjectByInfraObjectIdAndActionTypeVm ()
        };
        _providerMock.Setup(p => p.WorkflowInfraObject.GetWorkflowByInfraObjectIdAndActionType(infraId, actionType))
            .ReturnsAsync(workflow);

        var result = await _controller.GetWorkflowByInfraObjectIdAndActionType(infraId, actionType) as JsonResult;

        var json = JObject.FromObject(result.Value);
        Assert.True(json["Success"].Value<bool>());
        var dataArray = json["data"] as JArray;
        Assert.NotNull(dataArray);
        Assert.Single(dataArray);

    }

    [Fact]
    public async Task GetWorkflowByInfraObjectIdAndActionType_ReturnsEmptyList_WhenNoWorkflowFound()
    {
        var infraId = "infra123";
        var actionType = "actionType1";
        var workflow = new List<WorkflowInfraObjectByInfraObjectIdAndActionTypeVm>();
        _providerMock.Setup(p => p.WorkflowInfraObject.GetWorkflowByInfraObjectIdAndActionType(infraId, actionType))
            .ReturnsAsync(workflow);

        var result = await _controller.GetWorkflowByInfraObjectIdAndActionType(infraId, actionType) as JsonResult;

        Assert.NotNull(result);
        var jsonData = result.Value as List<WorkflowInfraObjectViewModel>;
            
    }
}