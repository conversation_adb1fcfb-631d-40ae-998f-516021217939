{"format": 1, "restore": {"D:\\Testcase\\Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj": {}}, "projects": {"D:\\Testcase\\Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Testcase\\Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj", "projectName": "ContinuityPatrol.Application", "projectPath": "D:\\Testcase\\Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Testcase\\Core\\ContinuityPatrol.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Testcase\\Core\\ContinuityPatrol.Domain\\ContinuityPatrol.Domain.csproj": {"projectPath": "D:\\Testcase\\Core\\ContinuityPatrol.Domain\\ContinuityPatrol.Domain.csproj"}, "D:\\Testcase\\Infrastructure\\ContinuityPatrol.Infrastructure\\ContinuityPatrol.Infrastructure.csproj": {"projectPath": "D:\\Testcase\\Infrastructure\\ContinuityPatrol.Infrastructure\\ContinuityPatrol.Infrastructure.csproj"}, "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Core\\ContinuityPatrol.Shared.Core.csproj": {"projectPath": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Core\\ContinuityPatrol.Shared.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Data.SqlClient.SNI.runtime": {"target": "Package", "version": "[6.0.2, )"}, "Microsoft.NETCore.Targets": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.21.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Testcase\\Core\\ContinuityPatrol.Domain\\ContinuityPatrol.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Testcase\\Core\\ContinuityPatrol.Domain\\ContinuityPatrol.Domain.csproj", "projectName": "ContinuityPatrol.Domain", "projectPath": "D:\\Testcase\\Core\\ContinuityPatrol.Domain\\ContinuityPatrol.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Testcase\\Core\\ContinuityPatrol.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Core\\ContinuityPatrol.Shared.Core.csproj": {"projectPath": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Core\\ContinuityPatrol.Shared.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Data.SqlClient.SNI.runtime": {"target": "Package", "version": "[6.0.2, )"}, "Microsoft.NETCore.Targets": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.21.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Testcase\\Infrastructure\\ContinuityPatrol.Infrastructure\\ContinuityPatrol.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Testcase\\Infrastructure\\ContinuityPatrol.Infrastructure\\ContinuityPatrol.Infrastructure.csproj", "projectName": "ContinuityPatrol.Infrastructure", "projectPath": "D:\\Testcase\\Infrastructure\\ContinuityPatrol.Infrastructure\\ContinuityPatrol.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Testcase\\Infrastructure\\ContinuityPatrol.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Testcase\\Core\\ContinuityPatrol.Domain\\ContinuityPatrol.Domain.csproj": {"projectPath": "D:\\Testcase\\Core\\ContinuityPatrol.Domain\\ContinuityPatrol.Domain.csproj"}, "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Infrastructure\\ContinuityPatrol.Shared.Infrastructure.csproj": {"projectPath": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Infrastructure\\ContinuityPatrol.Shared.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[14.0.0, )"}, "Microsoft.AspNetCore.SignalR": {"target": "Package", "version": "[1.2.0, )"}, "Microsoft.Data.SqlClient.SNI.runtime": {"target": "Package", "version": "[6.0.2, )"}, "Microsoft.Extensions.ObjectPool": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.NETCore.Targets": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.21.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Testcase\\Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Testcase\\Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj", "projectName": "ContinuityPatrol.Persistence", "projectPath": "D:\\Testcase\\Infrastructure\\ContinuityPatrol.Persistence\\ContinuityPatrol.Persistence.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Testcase\\Infrastructure\\ContinuityPatrol.Persistence\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Testcase\\Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj": {"projectPath": "D:\\Testcase\\Core\\ContinuityPatrol.Application\\ContinuityPatrol.Application.csproj"}, "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Infrastructure\\ContinuityPatrol.Shared.Infrastructure.csproj": {"projectPath": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Infrastructure\\ContinuityPatrol.Shared.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Data.SqlClient.SNI.runtime": {"target": "Package", "version": "[6.0.2, )"}, "Microsoft.NETCore.Targets": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.21.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Core\\ContinuityPatrol.Shared.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Core\\ContinuityPatrol.Shared.Core.csproj", "projectName": "ContinuityPatrol.Shared.Core", "projectPath": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Core\\ContinuityPatrol.Shared.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Testcase\\Shared\\ContinuityPatrol.Shared\\ContinuityPatrol.Shared.csproj": {"projectPath": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared\\ContinuityPatrol.Shared.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Data.SqlClient.SNI.runtime": {"target": "Package", "version": "[6.0.2, )"}, "Microsoft.Extensions.ObjectPool": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.NETCore.Targets": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.21.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Infrastructure\\ContinuityPatrol.Shared.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Infrastructure\\ContinuityPatrol.Shared.Infrastructure.csproj", "projectName": "ContinuityPatrol.Shared.Infrastructure", "projectPath": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Infrastructure\\ContinuityPatrol.Shared.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Core\\ContinuityPatrol.Shared.Core.csproj": {"projectPath": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Core\\ContinuityPatrol.Shared.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Data.SqlClient.SNI.runtime": {"target": "Package", "version": "[6.0.2, )"}, "Microsoft.NETCore.Targets": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.21.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "D:\\Testcase\\Shared\\ContinuityPatrol.Shared\\ContinuityPatrol.Shared.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared\\ContinuityPatrol.Shared.csproj", "projectName": "ContinuityPatrol.Shared", "projectPath": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared\\ContinuityPatrol.Shared.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 23.1\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.1\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"AspNetCore.HealthChecks.MySql": {"target": "Package", "version": "[9.0.0, )"}, "AspNetCore.HealthChecks.NpgSql": {"target": "Package", "version": "[9.0.0, )"}, "AspNetCore.HealthChecks.Oracle": {"target": "Package", "version": "[9.0.0, )"}, "AspNetCore.HealthChecks.SqlServer": {"target": "Package", "version": "[9.0.0, )"}, "AspNetCore.HealthChecks.UI.Client": {"target": "Package", "version": "[9.0.0, )"}, "AspNetCore.HealthChecks.Uris": {"target": "Package", "version": "[9.0.0, )"}, "AutoMapper": {"target": "Package", "version": "[14.0.0, )"}, "FluentValidation": {"target": "Package", "version": "[11.11.0, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.11.0, )"}, "LazyCache": {"target": "Package", "version": "[2.4.0, )"}, "LazyCache.AspNetCore": {"target": "Package", "version": "[2.4.0, )"}, "MailKit": {"target": "Package", "version": "[4.11.0, )"}, "MediatR": {"target": "Package", "version": "[12.5.0, )"}, "Microsoft.AspNetCore.SignalR": {"target": "Package", "version": "[1.2.0, )"}, "Microsoft.Data.SqlClient.SNI.runtime": {"target": "Package", "version": "[6.0.2, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.Extensions.Caching.StackExchangeRedis": {"target": "Package", "version": "[9.0.2, )"}, "Microsoft.Extensions.Diagnostics.HealthChecks": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.Extensions.Http.Polly": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.Extensions.ObjectPool": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.NETCore.Targets": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.21.2, )"}, "NWebsec.AspNetCore.Middleware": {"target": "Package", "version": "[3.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[9.0.4, )"}, "Oracle.EntityFrameworkCore": {"target": "Package", "version": "[9.23.80, )"}, "Oracle.ManagedDataAccess.Core": {"target": "Package", "version": "[23.8.0, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[9.0.0-preview.3.efcore.9.0.0, )"}, "Quartz": {"target": "Package", "version": "[3.14.0, )"}, "Quartz.AspNetCore": {"target": "Package", "version": "[3.14.0, )"}, "Quartz.Extensions.DependencyInjection": {"target": "Package", "version": "[3.14.0, )"}, "Quartz.Extensions.Hosting": {"target": "Package", "version": "[3.14.0, )"}, "Quartz.Serialization.Json": {"target": "Package", "version": "[3.14.0, )"}, "RestSharp": {"target": "Package", "version": "[112.1.0, )"}, "Seq.Api": {"target": "Package", "version": "[2024.3.0, )"}, "Serilog": {"target": "Package", "version": "[4.2.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Enrichers.ClientInfo": {"target": "Package", "version": "[2.1.2, )"}, "Serilog.Enrichers.CorrelationId": {"target": "Package", "version": "[3.0.1, )"}, "Serilog.Enrichers.Environment": {"target": "Package", "version": "[3.0.1, )"}, "Serilog.Enrichers.Thread": {"target": "Package", "version": "[4.0.0, )"}, "Serilog.Expressions": {"target": "Package", "version": "[5.0.0, )"}, "Serilog.Extensions.Logging": {"target": "Package", "version": "[9.0.1, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Sinks.AspNetCore.SignalR": {"target": "Package", "version": "[0.4.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.Seq": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Sinks.SignalR.Core": {"target": "Package", "version": "[0.1.2, )"}, "System.DirectoryServices.AccountManagement": {"target": "Package", "version": "[9.0.4, )"}, "System.Net.Http": {"target": "Package", "version": "[4.3.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}