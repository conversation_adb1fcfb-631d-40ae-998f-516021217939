﻿using AutoFixture;
using AutoMapper;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetDetail;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.MonitorService.Command.Create;
using ContinuityPatrol.Application.Features.MonitorService.Command.Update;
using ContinuityPatrol.Application.Features.MonitorService.Command.UpdateStatus;
using ContinuityPatrol.Application.Features.MonitorService.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.Server.Queries.GetDetail;
using ContinuityPatrol.Domain.ViewModels.BusinessServiceModel;
using ContinuityPatrol.Domain.ViewModels.InfraObjectModel;
using ContinuityPatrol.Domain.ViewModels.MonitorServicesListModel;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Manage.Controllers;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;

namespace ContinuityPatrol.Web.UnitTests.Areas.Manage.Controllers
{
    public class MonitoringServicesControllerTests
    {
        private readonly Mock<IPublisher> _publisherMock = new();
        private readonly Mock<IDataProvider> _providerMock = new();
        private readonly Mock<IMapper> _mapperMock = new();
        private readonly Mock<ILogger<MonitoringServicesController>> _loggerMock = new();
        private MonitoringServicesController _controller;

        public MonitoringServicesControllerTests()
        {
            Initialize();
        }
        public void Initialize()
        {
            _controller = new MonitoringServicesController(
                _publisherMock.Object,
                _providerMock.Object,
                _loggerMock.Object,
                _mapperMock.Object
            );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_Returns_View_With_Valid_Model()
        {
            var infraObjects = new PaginatedResult<InfraObjectListVm>();
            var businessServices = new List<BusinessServiceNameVm>();
            var monitorServices = new List<MonitorServiceListVm>();

            _providerMock.Setup(p => p.InfraObject.GetPaginatedInfraObjects(It.IsAny<GetInfraObjectPaginatedListQuery>()))
                .ReturnsAsync(infraObjects);

            _providerMock.Setup(p => p.BusinessService.GetBusinessServiceNames())
                .ReturnsAsync(businessServices);

            _providerMock.Setup(p => p.Monitor.GetMonitorServiceList())
                .ReturnsAsync(monitorServices);

            var result = await _controller.List(null, null) as ViewResult;

            var model = result.Model as MonitorServiceListViewModel;
            Assert.NotNull(model);
            Assert.Equal(businessServices, model.BusinessServiceList);
            Assert.Equal(monitorServices, model.MonitorServiceList);
            Assert.Equal(infraObjects, model.InfraObject);
        }

        [Fact]
        public async Task CreateOrUpdate_Create_New_MonitorService()
        {
            var monitorViewModel = new MonitorServiceListViewModel(); // Empty Id means create
            var createCommand = new CreateMonitorServiceCommand();
            var createResponse = new BaseResponse { Success = true, Message = "Created Successfully" };

            _mapperMock.Setup(m => m.Map<CreateMonitorServiceCommand>(monitorViewModel))
                .Returns(createCommand);

            _providerMock.Setup(p => p.Monitor.CreateAsync(createCommand))
                .ReturnsAsync(createResponse);

            var result = await _controller.CreateOrUpdate(monitorViewModel) as JsonResult;

            Assert.NotNull(result);

            var dict = result.Value as IDictionary<string, object>;

            Assert.True((bool)dict["Success"]);
            Assert.Equal("Created Successfully", dict["data"]);
        }

        [Fact]
        public async Task CreateOrUpdate_Update_Existing_MonitorService()
        {
            var monitorViewModel = new MonitorServiceListViewModel { Id = "MonitorId" }; // Non-empty Id means update
            var updateCommand = new UpdateMonitorServiceCommand();
            var updateResponse = new BaseResponse { Success = true, Message = "Updated Successfully" };

            _mapperMock.Setup(m => m.Map<UpdateMonitorServiceCommand>(monitorViewModel))
                .Returns(updateCommand);

            _providerMock.Setup(p => p.Monitor.UpdateAsync(updateCommand))
                .ReturnsAsync(updateResponse);

            var result = await _controller.CreateOrUpdate(monitorViewModel) as JsonResult;

            Assert.NotNull(result);

            var dict = result.Value as IDictionary<string, object>;

            Assert.True((bool)dict["Success"]);
            Assert.Equal("Updated Successfully", dict["data"]);
        }

        [Fact]
        public async Task Delete_Removes_MonitorService_And_Returns_Json()
        {
            var id = "monitor-id";
            var deleteResponse = new BaseResponse { Success = true, Message = "Deleted Successfully" };

            _providerMock.Setup(p => p.Monitor.DeleteAsync(id))
                .ReturnsAsync(deleteResponse);

            var result = await _controller.Delete(id) as JsonResult;

            Assert.NotNull(result);

            var dict = result.Value as IDictionary<string, object>;

            Assert.True((bool)dict["Success"]);
            Assert.Equal("Deleted Successfully", dict["data"]);
        }

        [Fact]
        public async Task GetPagination_Returns_Paginated_MonitorServices_As_Json()
        {
            var query = new GetMonitorServicePaginatedListQuery();
            var paginatedList = new PaginatedResult<MonitorServiceListVm>();

            _providerMock.Setup(p => p.Monitor.GetPaginatedMonitorServices(query))
                .ReturnsAsync(paginatedList);

            var result = await _controller.GetPagination(query) as JsonResult;

            Assert.NotNull(result);

            var dict = result.Value as IDictionary<string, object>;

            Assert.True((bool)dict["Success"]);
            Assert.Equal(paginatedList, dict["data"]);
        }

        //[Fact]
        //public async Task IsServiceNameExist_Returns_True_If_Name_Exists()
        //{
        //    var servicePath = "servicePath";
        //    var infraObjectId = "infraObjectId";
        //    var serverId = "serverId";
        //    var type = "type";
        //    var threadType = "threadType";
        //    var workflowId = "workflowId";
        //    var workflowType = "workflowType";
        //    var workflowName = "WorkflowName";
        //    var id = "id";

        //    _providerMock.Setup(p => p.Monitor.IsMonitorServiceNameExist(servicePath, infraObjectId, serverId, type, threadType, workflowId, workflowType, workflowName, id))
        //        .ReturnsAsync(true);

        //    var result = await _controller.IsServiceNameExist(servicePath, infraObjectId, serverId, type, threadType, workflowId, workflowType, workflowName, id);

        //    Assert.True(result);
        //}

        [Fact]
        public async Task MonitorStatus_Updates_Status_And_Returns_JsonResult()
        {
            var command = new UpdateMonitorServiceStatusCommand();
            var updateResponse = new BaseResponse { Success = true };

            _providerMock.Setup(p => p.Monitor.UpdateMonitorStatus(command))
                .ReturnsAsync(updateResponse);

            var result = await _controller.UpdateMonitorStatus(command) as JsonResult;

            dynamic jsonResponse = result.Value;
            var json = JsonConvert.SerializeObject(result); 
            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetServersByInfraObject_Returns_Servers_As_Json()
        {
            var infraObjectId = "infraObjectId";
            var infraObject = new InfraObjectDetailVm();

            _providerMock.Setup(p => p.InfraObject.GetInfraObjectById(infraObjectId))
                .ReturnsAsync(infraObject);

            var result = await _controller.GetInfraObjectById(infraObjectId) as JsonResult;

            Assert.Equal(infraObject, result.Value);
        }

        [Fact]
        public async Task GetServerByOSType_Returns_ServerData_As_Json()
        {
            var serverId = "serverId";
            var serverData = new ServerDetailVm();

            _providerMock.Setup(p => p.Server.GetByReferenceId(serverId))
                .ReturnsAsync(serverData);

            var result = await _controller.GetServerById(serverId) as JsonResult;

            Assert.Equal(serverData, result.Value);
        }
    }
}

