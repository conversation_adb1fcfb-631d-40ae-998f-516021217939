﻿using ContinuityPatrol.Application.Features.SolutionHistory.Queries.GetSolutionHistoryByActionId;
using ContinuityPatrol.Shared.Services.Base;
using ContinuityPatrol.Shared.Services.Contract;

namespace ContinuityPatrol.Services.Api.Impl.Admin;

public class SolutionHistoryService : ISolutionHistoryService
{
    private readonly IBaseClient _client;

    public SolutionHistoryService(IBaseClient client)
    {
        _client = client;
    }

    public async Task<List<SolutionHistoryByActionIdQueryVm>> GetSolutionHistoryByActionId(string actionId)
    {
        var request = new RestRequest($"api/v6/solutionhistory/by/{actionId}");

        return await _client.Get<List<SolutionHistoryByActionIdQueryVm>>(request);
    }
}
