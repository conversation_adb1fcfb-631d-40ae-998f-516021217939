using ContinuityPatrol.Application.Features.InfraObject.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.MonitorService.Command.Create;
using ContinuityPatrol.Application.Features.MonitorService.Command.Update;
using ContinuityPatrol.Application.Features.MonitorService.Command.UpdateStatus;
using ContinuityPatrol.Application.Features.MonitorService.Event.PaginatedView;
using ContinuityPatrol.Application.Features.MonitorService.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.MonitorServicesListModel;
using ContinuityPatrol.Shared.Core.Attributes;
using ContinuityPatrol.Shared.Core.Constants;
using ContinuityPatrol.Shared.Core.Permissions;
using ContinuityPatrol.Shared.Infrastructure.Extensions;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Attributes;

namespace ContinuityPatrol.Web.Areas.Manage.Controllers;

[Area("Manage")]

public class MonitoringServicesController : BaseController
{
    private readonly IPublisher _publisher;
    private readonly IDataProvider _provider;
    private readonly IMapper _mapper;
    private readonly ILogger<MonitoringServicesController> _logger;

    public MonitoringServicesController(IPublisher publisher,IDataProvider provider, ILogger<MonitoringServicesController> logger, IMapper mapper)
    {
        _publisher = publisher;
        _provider = provider;
        _mapper = mapper;
        _logger = logger;
    }
    [EventCode(EventCodes.MonitoringServices.List)]
    public async Task<IActionResult> List(string infraObjectId = null, string businessServiceId = null)
    {
        _logger.LogDebug("Entering List method in monitoring service");
        try
        {

            await _publisher.Publish(new MonitorServicePaginatedEvent());
            var businessServices = await _provider.BusinessService.GetBusinessServiceNames();
            var list = infraObjectId == null && businessServiceId == null ? await _provider.Monitor.GetMonitorServiceList() : await _provider.Monitor.GetByInfraObjectIdAndBusinessServiceId(infraObjectId, businessServiceId);
            var infra = await _provider.InfraObject.GetPaginatedInfraObjects(new GetInfraObjectPaginatedListQuery());
            var monitorViewModel = new MonitorServiceListViewModel
            {
                BusinessServiceList = businessServices,
                MonitorServiceList = list,
                InfraObject = infra
            };
            _logger.LogDebug("Successfully retrieved list in monitoring service.");

            return View(monitorViewModel);
        }
        catch(Exception ex)
        {
            _logger.Exception("An error occurred on monitoring service page while retrieving list.",ex);
            return ex.GetJsonException();

        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [Authorize(Policy = Permissions.Configuration.CreateAndEdit)]
    [EventCode(EventCodes.MonitoringServices.CreateOrUpdate)]
    public async Task<IActionResult> CreateOrUpdate(MonitorServiceListViewModel monitorServiceListViewModel)
    {
        _logger.LogDebug("Entering CreateOrUpdate method in monitoring service");
        var monitorId = monitorServiceListViewModel.Id;        

        try
        {
            if (string.IsNullOrEmpty(monitorId))
            {
                monitorServiceListViewModel.Status = "Stopped";              
                var createMonitor = _mapper.Map<CreateMonitorServiceCommand>(monitorServiceListViewModel);
                var result = await _provider.Monitor.CreateAsync(createMonitor);
                _logger.LogDebug($"Creating monitoring service  '{createMonitor.InfraObjectName}'.");
                _logger.LogDebug("CreateOrUpdate operation completed successfully in monitoring service, returning view.");               
                return Json(new { Success = true, data = result.Message });               
            }
            else
            {
                monitorServiceListViewModel.Status = monitorServiceListViewModel.Status;               
                var updateMonitor = _mapper.Map<UpdateMonitorServiceCommand>(monitorServiceListViewModel);
                _logger.LogDebug($"Updating monitoring service  '{updateMonitor.InfraObjectName}'.");
                _logger.LogDebug("CreateOrUpdate operation completed successfully in monitoring service, returning view.");
                var result = await _provider.Monitor.UpdateAsync(updateMonitor);
                return Json(new { Success = true, data = result.Message });              
            }           
        }
        catch (Exception ex)
        {       
            _logger.Exception("An error occurred on monitoring service page while processing the CreateOrUpdate request.", ex);

            return ex.GetJsonException();
        }

    }
    [Authorize(Policy = Permissions.Configuration.Delete)]
    [EventCode(EventCodes.MonitoringServices.Delete)]
    public async Task<IActionResult> Delete(string id)
    {
        _logger.LogDebug("Entering Delete method in monitoring service.");
        try
        {
            var monitor = await _provider.Monitor.DeleteAsync(id);
            _logger.LogDebug($"Successfully deleted record in monitoring service.");
            return Json(new { Success = true, data = monitor.Message });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on monitoring service page while deleting the record.", ex);
            return ex.GetJsonException();
        }
    }

    [HttpGet]
    [EventCode(EventCodes.MonitoringServices.Pagination)]
    public async Task<JsonResult> GetPagination(GetMonitorServicePaginatedListQuery query)
    {
        _logger.LogDebug("Entering GetPagination method in monitoring service.");
        try
        {
            var paginationList = await _provider.Monitor.GetPaginatedMonitorServices(query);

            _logger.LogDebug("Successfully retrieved pagination list in monitoring service.");

            return Json(new { Success = true, data = paginationList });
        }
        catch (Exception ex)
        {

            _logger.Exception("An error occurred on monitoring service page while retrieving pagination list", ex);

            return ex.GetJsonException();
        }
    }

    [HttpGet]
    [EventCode(EventCodes.MonitoringServices.IsExist)]
    public async Task<IActionResult> IsServiceNameExist(string servicePath, string infraObjectId, string serverId, string type, string threadType, string workflowId, string workflowType, string WorkflowName, string id)
    {
        _logger.LogDebug("Entering IsServiceNameExist method in monitoring service.");
        try
        {
            var nameExist = await _provider.Monitor.IsMonitorServiceNameExist(servicePath, infraObjectId, serverId, type, threadType, workflowId, workflowType, WorkflowName, id);

            _logger.LogDebug("Successfully retrieved the name exist detail in monitoring service.");

            return Json(new { Success = true, data = nameExist });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on monitoring service while retrieving the name exist detail.", ex);
            return Json(new { Success = false, data = ex.GetMessage() });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    [AntiXss]
    [EventCode(EventCodes.MonitoringServices.UpdateMonitorStatus)]
    public async Task<IActionResult> UpdateMonitorStatus(UpdateMonitorServiceStatusCommand updateMonitorServiceStatusCommand)
    {
        _logger.LogDebug("Entering UpdateMonitorStatus method in monitoring service.");
        try
        {
            var getStatus = await _provider.Monitor.UpdateMonitorStatus(updateMonitorServiceStatusCommand);

            _logger.LogDebug($"Successfully updated the monitor status to '{updateMonitorServiceStatusCommand.Status}' in monitoring service.");

            return Json(new { Success = true, data = getStatus });
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on monitoring service page while updating the monitor status.",ex );
            
            return ex.GetJsonException();
        }

    }
    [EventCode(EventCodes.MonitoringServices.GetInfraObjectById)]
    public async Task<IActionResult> GetInfraObjectById(string infraObjectId)
    {
        _logger.LogDebug("Entering GetInfraObjectById method in monitoring service.");
        try
        {
            var infraObject = await _provider.InfraObject.GetInfraObjectById(infraObjectId);

            _logger.LogDebug($"Successfully retrieved the infraObject detail by id '{infraObjectId} in monitoring service.");

            return Json(infraObject);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on monitoring service page while retrieving the infraObject detail by id.",ex);
            
            return ex.GetJsonException();
        }
    }
    [EventCode(EventCodes.MonitoringServices.GetByReferenceId)]
    public async Task<IActionResult> GetServerById(string serverId)
    {
        _logger.LogDebug("Entering GetServerById method in monitoring service.");
        try
        {
            var serverData = await _provider.Server.GetByReferenceId(serverId);

            _logger.LogDebug($"Successfully retrieved the server detail by id '{serverId} in monitoring service.");
           
            return Json(serverData);
        }
        catch (Exception ex)
        {
            _logger.Exception("An error occurred on monitoring service page while retrieving the server detail by id.",ex);
            
            return ex.GetJsonException();
        }
    }

}