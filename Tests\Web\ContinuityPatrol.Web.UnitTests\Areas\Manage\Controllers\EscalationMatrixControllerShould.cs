﻿using AutoFixture;
using AutoMapper;
using ContinuityPatrol.Application.Features.EscalationMatrix.Command.Create;
using ContinuityPatrol.Application.Features.EscalationMatrix.Command.Update;
using ContinuityPatrol.Application.Features.EscalationMatrix.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.EscalationMatrixLevel.Command.Create;
using ContinuityPatrol.Application.Features.EscalationMatrixLevel.Command.Update;
using ContinuityPatrol.Application.Features.TeamMaster.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.User.Queries.GetPaginatedList;
using ContinuityPatrol.Domain.ViewModels.EscalationMatrix;
using ContinuityPatrol.Domain.ViewModels.EscalationMatrixLevel;
using ContinuityPatrol.Domain.ViewModels.TeamMasterModel;
using ContinuityPatrol.Domain.ViewModels.UserModel.UserListModels;
using ContinuityPatrol.Domain.ViewModels.UserModel.UserViewModels;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Manage.Controllers;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using NuGet.ContentModel;

namespace ContinuityPatrol.Web.UnitTests.Areas.Manage.Controllers
{
    public class EscalationMatrixControllerTests
    {
        private readonly Mock<IPublisher> _publisherMock = new();
        private readonly Mock<ILogger<EscalationMatrixController>> _loggerMock = new();
        private readonly Mock<IMapper> _mapperMock = new();
        private readonly Mock<IDataProvider> _dataProviderMock = new();
        private EscalationMatrixController _controller;

        public EscalationMatrixControllerTests()
        {
            Initialize();
        }
        public void Initialize()
        {
            _controller = new EscalationMatrixController
             (
                _publisherMock.Object,
                _loggerMock.Object,
                _mapperMock.Object,
                _dataProviderMock.Object
             );
            _controller.ControllerContext.HttpContext = new DefaultHttpContext();
            _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
        }

        [Fact]
        public async Task List_Returns_ViewResult_With_EscalationMatrixViewModel()
        {
            var userList = new PaginatedResult<UserViewListVm>();
            var teamMasterList = new PaginatedResult<TeamMasterListVm>();

            _dataProviderMock.Setup(d => d.User.GetUserPaginatedList(It.IsAny<GetUserPaginatedListQuery>()))
                .ReturnsAsync(userList);
            _dataProviderMock.Setup(d => d.TeamMasterService.GetTeamConfigurationList(It.IsAny<GetTeamMasterPaginatedListQuery>()))
                .ReturnsAsync(teamMasterList);
            var result = await _controller.List() as ViewResult;

            var model = Assert.IsType<EscalationMatrixViewModel>(result.Model);
            //Assert.Equal(userList, model.PaginatedUserList);
            Assert.Equal(teamMasterList, model.PaginatedResultMasterList);
        }

        //[Fact]
        //public void  CreateOrUpdate_Creates_New_EscalationMatrix_When_Id_Is_Empty()
        //{
        //    var model = new EscalationMatrixViewModel { Id = string.Empty };
        //    var command = new CreateEscalationMatrrixCommand();
        //    var response = new BaseResponse { Success = true, Message = "Created successfully" };

        //    _mapperMock.Setup(m => m.Map<CreateEscalationMatrrixCommand>(model))
        //        .Returns(command);
        //    _dataProviderMock.Setup(d => d.EscalationService.CreateAsync(command))
        //        .ReturnsAsync(response);

        //    var result = _controller.CreateOrUpdate(model) ;

        //    Assert.NotNull(result);
            
        //}

        [Fact]
        public async Task CreateOrUpdate_Updates_EscalationMatrix_When_Id_Is_Not_Empty()
        {
            var viewModel = new Fixture().Create<EscalationMatrixViewModel>();
            var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
            dic.Add("id", "22");
            var collection = new FormCollection(dic);
            _controller.Request.Form = collection;
            var command = new UpdateEscalationMatrixCommand();

            var model = new EscalationMatrixViewModel { Id = "1" };
           
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mapperMock.Setup(m => m.Map<UpdateEscalationMatrixCommand>(model))
                .Returns(command);
            _dataProviderMock.Setup(d => d.EscalationService.UpdateAsync(command))
                .ReturnsAsync(response);

            var result = await _controller.CreateOrUpdate(model) ;
             Assert.NotNull(result);
        }

        [Fact]
        public async Task CreateOrUpdateEscLevel_Creates_New_EscalationMatrixLevel_When_Id_Is_Empty()
        {
            var model = new EscalationMatrixViewModel
            {
                EscalationMatrixLevelViewModel = new EscalationMatrixLevelViewModel { Id = string.Empty }
            };
            var command = new CreateEscalationMatrixLevelCommand();
            var response = new BaseResponse { Success = true, Message = "Created successfully" };

            _mapperMock.Setup(m => m.Map<CreateEscalationMatrixLevelCommand>(model.EscalationMatrixLevelViewModel))
                .Returns(command);
            _dataProviderMock.Setup(d => d.EscalationMatrixLevelService.CreateAsync(command))
                .ReturnsAsync(response);

            var result = await _controller.CreateOrUpdateEscLevel(model) ;
            Assert.NotNull (result);
            
        }

        [Fact]
        public void CreateOrUpdateEscLevel_Updates_EscalationMatrixLevel_When_Id_Is_Not_Empty()
        {
            var model = new EscalationMatrixViewModel
            {
                EscalationMatrixLevelViewModel = new EscalationMatrixLevelViewModel { Id = "1" }
            };
            var command = new UpdateEscalationMatrixLevelCommand();
            var response = new BaseResponse { Success = true, Message = "Updated successfully" };

            _mapperMock.Setup(m => m.Map<UpdateEscalationMatrixLevelCommand>(model.EscalationMatrixLevelViewModel))
                .Returns(command);
            _dataProviderMock.Setup(d => d.EscalationMatrixLevelService.UpdateAsync(command))
                .ReturnsAsync(response);

            var result =  _controller.CreateOrUpdateEscLevel(model) ;
            Assert.NotNull(result);
        }
       
        [Fact]
        public async Task Delete_Returns_RedirectToAction_On_Success()
        {
            var id = "1";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };

            _dataProviderMock.Setup(d => d.EscalationService.DeleteAsync(id))
                .ReturnsAsync(response);

            var result = await _controller.Delete(id);

            var jsonResult = Assert.IsType<JsonResult>(result);
            dynamic json = jsonResult.Value;
            Assert.NotNull(json);

            //Assert.True(json.Success);
            //Assert.Equal(response.Message, json.data);
        }

        [Fact]
        public async Task DeleteLevel_Returns_RedirectToAction_On_Success()
        {
            var id = "1";
            var response = new BaseResponse { Success = true, Message = "Deleted successfully" };

            _dataProviderMock.Setup(d => d.EscalationMatrixLevelService.DeleteAsync(id))
                .ReturnsAsync(response);

            var result = await _controller.DeleteLevel(id) as RedirectToActionResult;

            Assert.Equal("List", result.ActionName);
        }

        [Fact]
        public async Task GetPagination_Returns_JsonResult()
        {
            var query = new GetEscalationMatrixPaginatedListQuery();
            var paginatedList = new PaginatedResult<EscalationMatrixListVm>();
            _dataProviderMock.Setup(d => d.EscalationService.GetPaginatedEscalationList(query))
                .ReturnsAsync(paginatedList);

            var result = await _controller.GetPagination(query) as JsonResult;

            var resultData = Assert.IsType<PaginatedResult<EscalationMatrixListVm>>(result.Value);
            Assert.Equal(paginatedList, resultData);
        }

        [Fact]
        public async Task GetPaginationEscMatLevel_Returns_JsonResult()
        {
            var query = "some-query";
            var levels = new PaginatedResult<EscalationMatrixLevelListVm>();
            _dataProviderMock.Setup(d => d.EscalationMatrixLevelService.GetAllEscalationLevel(query))
                .ReturnsAsync(levels);

            var result = await _controller.GetPaginationEscMatLevel(query) as JsonResult;

            var resultData = Assert.IsType<PaginatedResult<EscalationMatrixLevelListVm>>(result.Value);
            Assert.Equal(levels, resultData);
        }

        //[Fact]
        //public async Task IsEscalationMatrixNameExist_Returns_Expected_Result()
        //{
        //    var teamName = "Team1";
        //    var exists = true;
        //    _dataProviderMock.Setup(d => d.EscalationService.IsEscalationMatrixExist(teamName))
        //        .ReturnsAsync(exists);

        //    var result = await _controller.IsEscalationMatrixNameExist(teamName);

        //    Assert.Equal(exists, result);
        //}

        [Fact]
        public async Task CheckEscalationLevelNameExist_Returns_Expected_Result()
        {
            var levelName = "Level1";
            var exists = true;
            _dataProviderMock.Setup(d => d.EscalationMatrixLevelService.IsEscalationMatrixLevelExist(levelName))
                .ReturnsAsync(exists);

            var result = await _controller.CheckEscalationLevelNameExist(levelName);

            Assert.Equal(exists, result);
        }
    }
}

