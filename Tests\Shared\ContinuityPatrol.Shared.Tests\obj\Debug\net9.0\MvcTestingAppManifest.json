{"ContinuityPatrol.Application, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null": "D:\\Testcase\\Core\\ContinuityPatrol.Application", "ContinuityPatrol.Domain, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null": "D:\\Testcase\\Core\\ContinuityPatrol.Domain", "ContinuityPatrol.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null": "D:\\Testcase\\Infrastructure\\ContinuityPatrol.Infrastructure", "ContinuityPatrol.Persistence, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null": "D:\\Testcase\\Infrastructure\\ContinuityPatrol.Persistence", "ContinuityPatrol.Shared.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Core", "ContinuityPatrol.Shared, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared", "ContinuityPatrol.Shared.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Infrastructure", "ContinuityPatrol.Shared.Services, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null": "D:\\Testcase\\Shared\\ContinuityPatrol.Shared.Services"}