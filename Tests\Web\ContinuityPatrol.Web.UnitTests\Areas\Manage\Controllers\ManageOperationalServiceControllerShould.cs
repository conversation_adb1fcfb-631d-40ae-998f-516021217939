﻿using ContinuityPatrol.Application.Features.InfraObject.Commands.UpdateState;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetDetail;
using ContinuityPatrol.Application.Features.InfraObject.Queries.GetPaginatedList;
using ContinuityPatrol.Application.Features.UserActivity.Commands.Create;
using ContinuityPatrol.Application.Features.UserInfraObject.Queries.GetByBusinessService;
using ContinuityPatrol.Domain.ViewModels.InfraObjectModel;
using ContinuityPatrol.Shared.Core.Contracts.Identity;
using ContinuityPatrol.Shared.Core.Responses;
using ContinuityPatrol.Shared.Core.Wrapper;
using ContinuityPatrol.Shared.Services.Contract;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Shared.Tests.Fakes;
using ContinuityPatrol.Web.Areas.Manage.Controllers;

namespace ContinuityPatrol.Web.UnitTests.Areas.Manage.Controllers;

public class ManageOperationalServiceControllerTests
{
    private readonly Mock<IInfraObjectService> _infraObjectServiceMock = new();
    private readonly Mock<IMapper> _mapperMock = new();
    private readonly Mock<IDataProvider> _dataProviderMock = new();
    private readonly Mock<ILogger<ManageOperationalServiceController>> _loggerMock = new();
    private readonly Mock<ILoggedInUserService> _mockloggeduserservice = new();
    private ManageOperationalServiceController _controller;


    public ManageOperationalServiceControllerTests()
    {
        Initialize();
    }
    public void Initialize()
    {
        _controller = new ManageOperationalServiceController(
		    _mockloggeduserservice.Object,
			_mapperMock.Object,
            _loggerMock.Object,
            _dataProviderMock.Object
        );
        _controller.ControllerContext.HttpContext = new DefaultHttpContext();
        _controller.TempData = TempDataFakes.GeTempDataDictionary(_controller.HttpContext, "Test", "Test");
    }

    [Fact]
    public async Task List_Returns_ViewResult_With_InfraObjectViewModel()
    {
        var paginatedInfraObjects = new PaginatedResult<InfraObjectListVm>();
        var createUserActivityCommand = new Fixture().Create<CreateUserActivityCommand>();
		_dataProviderMock
			.Setup(service => service.InfraObject.GetPaginatedInfraObjects(It.IsAny<GetInfraObjectPaginatedListQuery>()))
            .ReturnsAsync(paginatedInfraObjects);
        _dataProviderMock.Setup(x => x.UserActivity.CreateAsync(createUserActivityCommand)).ReturnsAsync(new BaseResponse { Success=true,Message="created successfuly"});

		var result =  await _controller.List() as ViewResult;
        Assert.NotNull(result);
        
    }

    [Fact]
    public async Task Update_ValidInfraObject_Returns_RedirectToActionResult()
    {
        var dic = new Dictionary<string, Microsoft.Extensions.Primitives.StringValues>();
        dic.Add("id", "22");
        var collection = new FormCollection(dic);
        _controller.Request.Form = collection;

        var command = new UpdateInfraObjectStateCommand
        {
            UpdateInfraObjectStates = new List<UpdateInfraObjectListCommand>
            {
                new UpdateInfraObjectListCommand { Id = "1" },
                new UpdateInfraObjectListCommand { Id = "2" }
            },
        };

        var rr = new InfraObjectDetailVm();
        var response = new BaseResponse { Success = true, Message = "Update successful" };

        _infraObjectServiceMock
            .Setup(service => service.UpdateInfraObjectState(It.IsAny<UpdateInfraObjectStateCommand>()))
            .ReturnsAsync(response);

        _dataProviderMock
            .Setup(dp => dp.InfraObject.UpdateInfraObjectState(It.IsAny<UpdateInfraObjectStateCommand>()))
            .ReturnsAsync(response);

        var result = await _controller.UpdateInfraObjectState(command) as JsonResult;

        dynamic json = result!.Value;
        Assert.NotNull(result);
        //Assert.Equal(response.Success, json!.data.Success);
        //Assert.Equal(response.Message, json.data.Message);
    }

    [Fact]
    public async Task GetPagination_Returns_JsonResult_With_PaginatedResult()
    {
        var query = new GetInfraObjectPaginatedListQuery();
        var paginatedList = new PaginatedResult<InfraObjectListVm>();
        _infraObjectServiceMock
            .Setup(service => service.GetPaginatedInfraObjects(query))
            .ReturnsAsync(paginatedList);

        var result = await _controller.GetPagination(query);
        Assert.NotNull(result);
           
    }

    [Fact]
    public void GetAllInfraObjectList_Returns_GetUserInfraObjectByBusinessServiceVm()
    {
        var data = "some-data";
        var userInfraObject = new GetUserInfraObjectByBusinessServiceVm();
        _dataProviderMock
            .Setup(dp => dp.UserInfraObject.GetUserInfraObjects(data))
            .ReturnsAsync(userInfraObject);

        var result =  _controller.GetUserInfraObjectList(data) ;

        Assert.NotNull( result);
    }
}