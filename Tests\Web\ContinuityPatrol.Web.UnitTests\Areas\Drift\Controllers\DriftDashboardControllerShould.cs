﻿using AutoMapper;
using ContinuityPatrol.Shared.Services.Provider;
using ContinuityPatrol.Web.Areas.Drift.Controllers;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;

namespace ContinuityPatrol.Web.UnitTests.Areas.Drift.Controllers  
{
    public class DriftDashboardControllerTests
    {
        private readonly DriftDashboardController _controller;
        private readonly Mock<IDataProvider> _mockdataprovider=new ();
        private readonly Mock<IMapper> _mockmapper = new();
        private readonly Mock<IPublisher> _mockPublisher = new();
        private readonly Mock<ILogger<DriftDashboardController>> _mockLogger = new();
        public DriftDashboardControllerTests()
        {         
            _controller = new DriftDashboardController(_mockdataprovider.Object, _mockmapper.Object, _mockLogger.Object, _mockPublisher.Object);
        }

        [Fact]
        public async Task List_Returns_ViewResult()
        {
            var result = await _controller.List();

            var viewResult = Assert.IsType<ViewResult>(result);
        }
    }
}


